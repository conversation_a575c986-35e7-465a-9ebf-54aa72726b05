import './search-history-modal.css';

import { Trans, useLingui } from '@lingui/react/macro';
import { useReducer, useRef } from 'preact/hooks';

import {
  clearAllSearchHistory,
  getSearchHistory,
  removeFromSearchHistory,
} from '../utils/search-history';
import showToast from '../utils/show-toast';

import Icon from './icon';
import Link from './link';
import { generateSearchItemData } from './search-form';

export default function SearchHistoryModal({ instance, onClose }) {
  const { t } = useLingui();
  const historyListRef = useRef(null);
  const [, reload] = useReducer((c) => c + 1, 0);

  const handleClearAll = () => {
    clearAllSearchHistory();
    showToast({
      text: t`Cleared recent searches`,
      delay: 1000,
    });
    onClose?.();
  };

  const handleRemoveItem = (query, queryType) => {
    removeFromSearchHistory(query, queryType);
    reload();
  };

  const history = getSearchHistory();

  return (
    <div id="search-history-container" class="sheet" tabIndex="-1">
      {!!onClose && (
        <button type="button" class="sheet-close" onClick={onClose}>
          <Icon icon="x" alt={t`Close`} />
        </button>
      )}
      <header>
        <h2>
          <Trans>Recent searches</Trans>
        </h2>
      </header>
      <main>
        <ul class="search-history-list" ref={historyListRef}>
          {history.length === 0 ? (
            <div class="search-history-empty">
              <Icon icon="search" size="xxl" class="insignificant" />
              <p class="insignificant">
                <Trans>No recent searches</Trans>
              </p>
            </div>
          ) : (
            history.map((historyItem) => {
              const { label, to, icon } = generateSearchItemData(
                historyItem.query,
                historyItem.queryType,
                instance,
              );

              return (
                <li
                  key={`${historyItem.query}-${historyItem.queryType}-${historyItem.timestamp}`}
                  class="search-history-item"
                >
                  <Link to={to} class="search-history-link" onClick={onClose}>
                    <Icon icon={icon} class="more-insignificant" />
                    <span class="search-history-label">{label}</span>
                  </Link>
                  <button
                    type="button"
                    class="plain4 small"
                    onClick={() =>
                      handleRemoveItem(historyItem.query, historyItem.queryType)
                    }
                  >
                    <Icon icon="trash" alt={t`Clear`} />
                  </button>
                </li>
              );
            })
          )}
        </ul>
      </main>
      <footer>
        <button
          type="button"
          class="light small"
          onClick={handleClearAll}
          disabled={history.length <= 0}
        >
          <span>
            <Trans>Clear all</Trans>
          </span>
        </button>
        <small class="insignificant">Max 10 recent searches</small>
      </footer>
    </div>
  );
}
