.recent-searches {
  margin-block: 16px;
}

.recent-searches-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: bold;
  color: var(--text-insignificant-color);
  border-bottom: 1px solid var(--outline-color);
  margin-bottom: 8px;
}

.recent-searches-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.recent-searches-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  text-decoration: none;
  color: var(--link-color);
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.recent-searches-item:hover {
  background-color: var(--bg-faded-color);
  color: var(--text-color);
}

.recent-searches-see-all {
  color: var(--text-insignificant-color);
  font-size: 14px;
}

.recent-searches-see-all:hover {
  color: var(--text-color);
}
