.recent-searches {
  margin-block: 16px;
}

.recent-searches-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: bold;
  color: var(--text-insignificant-color);
  border-bottom: 1px solid var(--outline-color);
  margin-bottom: 8px;
}

.recent-searches-list {
  list-style: none;
  margin: 0;
  padding: 0;
  background-color: var(--bg-faded-color);
  border-radius: 8px;
  overflow: hidden;
}

.recent-searches-item {
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.recent-searches-item:not(:last-child) {
  border-bottom: 1px solid var(--bg-color);
}

.recent-searches-link {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: var(--link-color);
}

.recent-searches-link:hover {
  color: var(--text-color);
}

.recent-searches-label :is(mark, q) {
  color: var(--text-color);
  background-color: var(--link-bg-color);
  unicode-bidi: isolate;
  direction: initial;
}

.recent-searches-see-all {
  padding: 8px 16px;
  text-align: center;
}

.recent-searches-footer {
  padding: 0 16px 16px;
  display: flex;
  justify-content: space-between;
  gap: 8px;
  align-items: center;
}

.recent-searches-empty {
  padding: 32px 16px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}
