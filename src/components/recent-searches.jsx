import { Trans } from '@lingui/react/macro';

import { api } from '../utils/api';
import { addToSearchHistory, getSearchHistory } from '../utils/search-history';
import states from '../utils/states';

import Icon from './icon';
import Link from './link';
import { generateSearchItemData } from './search-form';

export default function RecentSearches({ onItemClick }) {
  const { instance } = api();
  const history = getSearchHistory();

  if (history.length === 0) {
    return null;
  }

  return (
    <div class="recent-searches">
      <div class="recent-searches-header">
        <Icon icon="history" size="s" />
        <Trans>Recent Searches</Trans>
      </div>
      <div class="recent-searches-list">
        {history.map((historyItem) => {
          const { label, to, icon } = generateSearchItemData(
            historyItem.query,
            historyItem.queryType,
            instance,
          );

          return (
            <Link
              key={`${historyItem.query}-${historyItem.queryType}-${historyItem.timestamp}`}
              to={to}
              class="recent-searches-item"
              onClick={(e) => {
                addToSearchHistory(historyItem.query, historyItem.queryType);
                onItemClick?.(e);
              }}
            >
              <Icon icon={icon} class="more-insignificant" />
              <span>{label}</span>
            </Link>
          );
        })}
        <button
          type="button"
          class="plain recent-searches-item recent-searches-see-all"
          onClick={() => {
            states.showSearchHistory = { instance };
            onItemClick?.();
          }}
        >
          <Icon icon="more2" class="more-insignificant" />
          <span>
            <Trans>See all</Trans>
          </span>
        </button>
      </div>
    </div>
  );
}
