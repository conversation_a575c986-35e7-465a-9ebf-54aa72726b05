import { Trans, useLingui } from '@lingui/react/macro';
import { useReducer } from 'preact/hooks';

import { api } from '../utils/api';
import {
  addToSearchHistory,
  clearAllSearchHistory,
  getSearchHistory,
  removeFromSearchHistory,
} from '../utils/search-history';
import showToast from '../utils/show-toast';

import Icon from './icon';
import Link from './link';
import { generateSearchItemData } from './search-form';

export default function RecentSearches({ onItemClick, showActions = false }) {
  const { t } = useLingui();
  const { instance } = api();
  const [, reload] = useReducer((c) => c + 1, 0);
  const history = getSearchHistory();

  const handleClearAll = () => {
    clearAllSearchHistory();
    showToast({
      text: t`Cleared recent searches`,
      delay: 1000,
    });
    reload();
  };

  const handleRemoveItem = (query, queryType) => {
    removeFromSearchHistory(query, queryType);
    reload();
  };

  if (history.length === 0) {
    return showActions ? (
      <div class="recent-searches">
        <div class="recent-searches-header">
          <Icon icon="history" size="s" />
          <Trans>Recent Searches</Trans>
        </div>
        <div class="recent-searches-empty">
          <Icon icon="search" size="xxl" class="insignificant" />
          <p class="insignificant">
            <Trans>No recent searches</Trans>
          </p>
        </div>
      </div>
    ) : null;
  }

  return (
    <div class="recent-searches">
      <div class="recent-searches-header">
        <Icon icon="history" size="s" />
        <Trans>Recent Searches</Trans>
      </div>
      <ul class="recent-searches-list">
        {history.map((historyItem) => {
          const { label, to, icon } = generateSearchItemData(
            historyItem.query,
            historyItem.queryType,
            instance,
          );

          return (
            <li
              key={`${historyItem.query}-${historyItem.queryType}-${historyItem.timestamp}`}
              class="recent-searches-item"
            >
              <Link
                to={to}
                class="recent-searches-link"
                onClick={(e) => {
                  addToSearchHistory(historyItem.query, historyItem.queryType);
                  onItemClick?.(e);
                }}
              >
                <Icon icon={icon} class="more-insignificant" />
                <span class="recent-searches-label">{label}</span>
              </Link>
              {showActions && (
                <button
                  type="button"
                  class="plain4 small"
                  onClick={() =>
                    handleRemoveItem(historyItem.query, historyItem.queryType)
                  }
                >
                  <Icon icon="trash" alt={t`Clear`} />
                </button>
              )}
            </li>
          );
        })}
      </ul>
      {!showActions && (
        <div class="recent-searches-see-all">
          <Link to="/search" class="plain button">
            <Icon icon="more2" class="more-insignificant" />
            <span>
              <Trans>See all</Trans>
            </span>
          </Link>
        </div>
      )}
      {showActions && (
        <div class="recent-searches-footer">
          <button
            type="button"
            class="light small"
            onClick={handleClearAll}
            disabled={history.length <= 0}
          >
            <span>
              <Trans>Clear all</Trans>
            </span>
          </button>
          <small class="insignificant">Max 10 recent searches</small>
        </div>
      )}
    </div>
  );
}
