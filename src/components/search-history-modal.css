#search-history-container {
  .search-history-list {
    list-style: none;
    margin: 0;
    padding: 0;
    background-color: var(--bg-faded-color);
    border-radius: 8px;
    overflow: hidden;
  }

  .search-history-item {
    padding: 8px;
    display: flex;
    align-items: center;
    gap: 8px;

    &:not(:last-child) {
      border-bottom: 1px solid var(--bg-color);
    }
  }

  .search-history-link {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: var(--link-color);
  }

  .search-history-link:hover {
    color: var(--text-color);
  }

  .search-history-label :is(mark, q) {
    color: var(--text-color);
    background-color: var(--link-bg-color);
    unicode-bidi: isolate;
    direction: initial;
  }

  footer {
    padding: 0 16px 16px;
    display: flex;
    justify-content: space-between;
    gap: 8px;
    align-items: center;
  }
}
