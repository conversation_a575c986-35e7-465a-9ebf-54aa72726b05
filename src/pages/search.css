#search-page .deck > header .header-grid {
  grid-template-columns: auto 1fr auto;
}
#search-page header {
  input {
    width: 100%;
    padding: 8px 16px;
    border: 0;
    border-radius: 999px;
    background-color: var(--bg-faded-color);
    border: 2px solid transparent;

    &:focus {
      outline: 0;
      background-color: var(--bg-color);
      border-color: var(--link-color);
    }

    #columns & {
      font-weight: bold;
      background-color: transparent;
      text-align: center;
      padding-inline: 8px;
      text-overflow: ellipsis;
    }
  }
}

#columns #search-page {
  .header-grid {
    .header-side {
      min-width: 40px;

      &:last-of-type {
        button {
          display: block;

          &:not(:hover, :focus) {
            color: var(--text-insignificant-color);
          }
        }
      }
    }
  }
}

#search-page h2 {
  a {
    .icon {
      vertical-align: middle;
      transition: margin 0.2s;
    }
    &:hover .icon {
      margin-inline-start: 4px;
    }
  }
}

#search-page ul.accounts-list {
  display: flex;
  flex-wrap: wrap;
}
#search-page ul.accounts-list li {
  flex-basis: 320px;
  display: flex;
  padding: 8px 16px;
  gap: 8px;
  /* align-items: center; */
  flex-grow: 1;

  .account-block {
    align-items: flex-start;
  }
}

ul.link-list.hashtag-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
}

ul.link-list.hashtag-list li a {
  border-radius: var(--radius);
}

@media (min-width: 40em) {
  #search-page {
    header input {
      background-color: var(--bg-color);
    }

    .filter-bar {
      margin-top: 8px;
    }
  }
}

.search-popover-container {
  position: relative;
}
.search-popover {
  position: absolute;
  inset-inline-start: 8px;
  max-width: calc(100% - 16px);
  background-color: var(--bg-color);
  border: 1px solid var(--outline-color);
  box-shadow: 0 4px 24px var(--drop-shadow-color);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  animation: appear-smooth 0.2s ease-out;
  overflow: hidden;
}
.search-popover[hidden] {
  display: none;
}
.search-popover-item {
  text-decoration: none;
  padding: 8px;
  padding-inline-end: 16px;
  display: flex;
  gap: 8px;
  align-items: center;
  border-radius: 0;
}
.search-popover-item[hidden] {
  display: none;
}
.search-popover-item:is(:hover, :focus, .focus) {
  background-color: var(--link-bg-color);
  color: var(--text-color);
}
.search-popover-item:is(:focus, .focus) {
  box-shadow: inset 4px 0 0 0 var(--button-bg-color);
  :dir(rtl) & {
    box-shadow: inset -4px 0 0 0 var(--button-bg-color);
  }
}
.search-popover-item :is(mark, q) {
  color: var(--text-color);
  background-color: var(--link-bg-color);
  unicode-bidi: isolate;
  direction: initial;
}
.search-popover-item:is(:hover, :focus, .focus) :is(mark, q) {
  background-color: var(--link-bg-color);
}
.search-popover:hover .search-popover-item.focus:not(:hover, :focus),
.search-popover:hover
  .search-popover-item.focus:not(:hover, :focus)
  :is(mark, q) {
  /* background-color: unset; */
  /* color: unset; */
}
.search-popover-item > span {
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.search-popover-item:is(:hover, :focus, .focus) > .icon {
  opacity: 1;
}

.search-popover-header {
  background-image: linear-gradient(
    to bottom,
    var(--outline-color),
    transparent
  );
  padding: 8px 10px;
  color: var(--text-insignificant-color);
  font-size: 12px;
  text-transform: uppercase;
  pointer-events: none;
  user-select: none;
  display: flex;
  align-items: center;
  gap: 4px;
}

.search-history-see-all {
  font-size: smaller;
  color: var(--text-insignificant-color) !important;
}
.search-history-see-all:is(:hover, :focus, .focus) {
  color: var(--text-color) !important;
  background-color: var(--link-bg-color) !important;
}

/* Hide recent searches in popover when on search page */
#search-page .search-popover-recent-searches {
  display: none;
}

/* Recent Searches Component */
.recent-searches {
  padding: 16px;
}

.recent-searches-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  color: var(--text-insignificant-color);
  font-size: smaller;
}

.recent-searches-list {
  list-style: none;
  margin: 0;
  padding: 0;
  background-color: var(--bg-faded-color);
  border-radius: 8px;
  overflow: hidden;
}

.recent-searches-item {
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.recent-searches-item:not(:last-child) {
  border-bottom: 1px solid var(--bg-color);
}

.recent-searches-link {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: var(--link-color);
}

.recent-searches-link:hover {
  color: var(--text-color);
}

.recent-searches-label :is(mark, q) {
  color: var(--text-color);
  background-color: var(--link-bg-color);
  unicode-bidi: isolate;
  direction: initial;
}

.recent-searches-see-all {
  padding: 8px 16px;
  text-align: center;
}

.recent-searches-footer {
  padding-block: 12px;
  display: flex;
  justify-content: space-between;
  gap: 8px;
  align-items: center;
}

.recent-searches-empty {
  padding: 32px 16px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}
